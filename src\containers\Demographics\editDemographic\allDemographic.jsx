import React, { useEffect, useState } from 'react';
import {
  FormControl,
  TextField,
  Grid,
  Box,
  Stack,
  Select,
  InputLabel,
  MenuItem,
  FormHelperText,
  Typography,
  Button,
} from '@mui/material';
import { AddressFields } from './AddressFields';
import { Add } from '@mui/icons-material';
import { sortFields } from '../demographicUtility';
import dataValidation from '../../../utils/dataValidation/dataValidation';
import { contactFieldValidation } from '../../../utils/helpers/validation';
import { individualFieldValidation } from '../../../utils/helpers/validation';
import { convertInDateFormat } from '../../../utils/helpers/date-time';
import { CambianDatePicker, CambianTooltip, Loader } from '@/components';
import { parseISO } from 'date-fns';
import { useTranslation } from 'react-i18next';
import { MaskTextField } from '../../../components/MaskTextField';
import { HealthcareIdFields } from './HealthcareIdFields';
import { ContactField } from '@/components';
import { useSendOtp, useVerifyOtp } from '../api';

export const AllDemographic = (props) => {
  const {
    contact,
    setTriggerUseEffect,
    preferredContactMethod,
    handlePreferredContactMethodCallback,
    demographic,
    demographicFields,
    individual,
    handleIndividualChangeCallback,
    fields,
    isClientSummary,
    validationData,
    index,
    handleContactChangeCallback,
    userInfoSuccessData,
    otpVerificationEnabled,
    setShowValidationErrors,
    onVerificationSuccess,
    primaryPhoneVerified,
    primaryEmailVerified,
    verifiedValues,
  } = props;
  const { t, i18n } = useTranslation();
  const contactFieldTexts = {
    verifyEmail: t('Enter Verification Code'),
    verifyPhone: t('Enter Verification Code'),
    verificationSubtitle: t('A verification code has been sent to '),
    verificationSubtitleEmail: t('. Please enter it to verify your email address.'),
    verificationSubtitlePhone: t('. Please enter it to verify your phone number.'),
    verificationCode: t('Verification code'),
    verificationError: t('We are having trouble verifying that code. Please try again.'),
    verify: t('Verify'),
    clickHere: t('Click here'),
    clickToVerify: t('Click to verify'),
    somethingWentWrong: t('Something went wrong. Please try again.'),
    verified: t('Verified'),
    primary: t('Primary'),
    setAsPrimary: t('Set as Primary'),
    deleteEmail: t('Delete email'),
    deletePhone: t('Delete phone'),
    resendCode: t('Resend Verification Code'),
    toVerifyEmail: t('You must verify your email to continue. '),
    toVerifyPhone: t('You must verify your phone to continue. '),
  };
  const genderOptions = [
    { displayName: t('male'), value: 'male' },
    { displayName: t('female'), value: 'female' },
    { displayName: t('unknown'), value: 'unknown' },
  ];
  const allowNotificationsOptions = [
    { displayName: t('yes'), value: true, disabled: false },
    { displayName: t('no'), value: false, disabled: false },
  ];

  const [gender, setGender] = useState(demographicFields ? individual?.gender?.toLowerCase() : '');
  const [date, setDate] = useState(demographicFields && individual?.dateOfBirth ? individual?.dateOfBirth : null);

  const [healthcareIds, setHealthcareIds] = useState(
    individual?.healthCareIds[0]?.value ? individual?.healthCareIds : [],
  );

  const [contactMethodOptions, setContactMethodOptions] = useState([
    {
      id: 'EMAIL',
      value: t('email'),
      disabled: false,
    },
    {
      id: 'PHONE',
      value: t('phone'),
      disabled: false,
    },
  ]);
  useEffect(() => {
    setContactMethodOptions([
      {
        id: 'EMAIL',
        value: t('email'),
        disabled: false,
      },
      {
        id: 'PHONE',
        value: t('phone'),
        disabled: false,
      },
    ]);
  }, [i18n.language]);

  const [emails, setEmails] = useState(
    contact?.emailAddresses?.length ? contact?.emailAddresses : [{ emailAddress: '', primary: true }],
  );

  const [phones, setPhones] = useState(
    contact?.phoneNumbers?.length ? contact?.phoneNumbers : [{ phoneNumber: '', primary: true }],
  );
  let primaryPhoneIndex = contact?.phoneNumbers.length
    ? contact?.phoneNumbers.findIndex((phone) => phone.primary === true)
    : 0;
  let primaryEmailIndex = contact?.emailAddresses.length
    ? contact?.emailAddresses.findIndex((email) => email.primary === true)
    : 0;
  const [addresses, setAddresses] = useState(contact?.addresses || []);
  const { mutate: sendOtp, isPending: isSendingOtp } = useSendOtp();
  const { mutate: verifyOtp, isPending: isVerifyingOtp } = useVerifyOtp();

  const [sendOtpError, setSendOtpError] = useState({});

  const handleVerificationClick = ({ type, value, onSuccess }) => {
    setSendOtpError((prev) => ({ ...prev, [value]: false }));
    sendOtp(
      { type, value },
      {
        onSuccess: () => {
          onSuccess();
        },
        onError: (error) => {
          console.error('Failed to send OTP:', error);
          setSendOtpError((prev) => ({ ...prev, [value]: true }));
        },
      },
    );
  };

  const handleVerify = ({ type, value, otp, onSuccess }) => {
    verifyOtp(
      { type, value, otp },
      {
        onSuccess: (response) => {
          onSuccess(response);
          if (response?.result === 'VERIFIED') {
            if (type === 'Email') {
              onVerificationSuccess('email', true, value);
            } else if (type === 'Phone') {
              onVerificationSuccess('phone', true, value);
            }
            console.log('Contact Field Verified!');
          }
        },
        onError: (error) => {
          console.error('Failed to verify OTP:', error);
        },
      },
    );
  };

  useEffect(() => {
    setEmails(contact?.emailAddresses?.length ? contact?.emailAddresses : [{ emailAddress: '', primary: true }]);
    setPhones(contact?.phoneNumbers?.length ? contact?.phoneNumbers : [{ phoneNumber: '', primary: true }]);
    setAddresses(contact?.addresses || []);
    primaryPhoneIndex = contact?.phoneNumbers.length
      ? contact?.phoneNumbers.findIndex((phone) => phone.primary === true)
      : 0;
    primaryEmailIndex = contact?.emailAddresses.length
      ? contact?.emailAddresses.findIndex((email) => email.primary === true)
      : 0;
    setTriggerUseEffect((oldValue) => !oldValue);
  }, [contact]);

  useEffect(() => {
    setGender(individual?.gender?.toLowerCase());
    setDate(individual?.dateOfBirth || null);
    setHealthcareIds(individual?.healthCareIds[0]?.value ? individual?.healthCareIds : []);
  }, [individual]);

  const handleIndividualChange = (value, fieldName, isRequired) => {
    if (fieldName === 'gender') {
      setGender(value);
    }
    if (fieldName === 'subscribeToNotifications') {
      demographicFields.subscribeToNotifications = value;
    }
    if (fieldName === 'dateOfBirth') {
      setDate(value);
    }
    if (fieldName === 'healthCareIds') {
      demographicFields.individuals[index].healthCareIds = [...value];
    } else {
      individual[fieldName] = value;
      demographicFields.individuals[index][fieldName] = value;
    }
    handleIndividualChangeCallback(
      individual,
      index,
      individualFieldValidation(value, fieldName, index, isRequired, individual),
    );
  };

  const handleContactChange = (value, fieldName, isRequired, validationMeta = []) => {
    demographicFields.contact[fieldName] = value;
    if (!validationData.contact) {
      validationData.contact = {};
    }
    if (Array.isArray(value)) {
      validationData.contact[fieldName] = value.map((_, idx) => {
        const meta = validationMeta[idx];
        return meta && meta.error ? meta.helperText : '';
      });
    } else {
      validationData.contact[fieldName] =
        validationMeta[0] && validationMeta[0].error ? validationMeta[0].helperText : '';
    }
    handleContactChangeCallback(demographicFields.contact, validationData);

    if (fieldName === 'emailAddresses') {
      const primaryEmail = value.find((email) => email.primary)?.emailAddress;

      if (primaryEmail) {
        if (verifiedValues.has(primaryEmail)) {
          onVerificationSuccess('email', true, primaryEmail);
        } else {
          onVerificationSuccess('email', false, primaryEmail);
        }
      }
    } else if (fieldName === 'phoneNumbers') {
      const primaryPhone = value.find((phone) => phone.primary)?.phoneNumber;
      if (primaryPhone) {
        if (verifiedValues.has(primaryPhone)) {
          onVerificationSuccess('phone', true, primaryPhone);
        } else {
          onVerificationSuccess('phone', false, primaryPhone);
        }
      }
    }

    if (fieldName.toUpperCase() === preferredContactMethod?.toUpperCase() && value === '') {
      handlePreferredContactMethodCallback('');
    }

    setContactMethodOptions((prevContactMethod) => {
      const updatedContactOptions = [...prevContactMethod];
      return updatedContactOptions.map((contactMethod) => {
        if (contactMethod.id === 'EMAIL' && fieldName === 'emailAddresses' && value === '') {
          contactMethod.disabled = true;
        } else if (contactMethod.id === 'PHONE' && fieldName === 'phoneNumbers' && value === '') {
          contactMethod.disabled = true;
        } else if (contactMethod.id === 'EMAIL' && fieldName === 'emailAddresses' && value) {
          contactMethod.disabled = false;
        } else if (contactMethod.id === 'PHONE' && fieldName === 'phoneNumbers' && value) {
          contactMethod.disabled = false;
        } else if (
          (contact.phoneNumbers === '' && fieldName === 'emailAddresses') ||
          (contact.emailAddresses === '' && fieldName === 'phoneNumbers')
        ) {
          contactMethod.disabled = true;
        } else {
          contactMethod.disabled = false;
        }
        return contactMethod;
      });
    });
  };

  const getFirstName = (item) => {
    return (
      <Grid item xs={12} sm={10} md={6} lg={5} sx={{ mt: 2, pl: { xs: 1, sm: 3 }, pr: 1 }}>
        <FormControl fullWidth variant="outlined">
          <TextField
            value={individual?.firstName}
            onChange={(e) => handleIndividualChange(e.target.value, 'firstName', item.isMandatory)}
            label={item.display}
            name="firstName"
            size="small"
            required={item.isMandatory}
            inputProps={{
              readOnly: isClientSummary,
            }}
            error={validationData?.individuals ? !!validationData?.individuals?.[index]?.firstName : false}
            helperText={validationData?.individuals ? validationData?.individuals?.[index]?.firstName : ''}
            autoComplete="off"
          />
        </FormControl>
      </Grid>
    );
  };

  const getLastName = (item) => {
    return (
      <Grid item xs={12} sm={10} md={6} lg={5} sx={{ mt: 2, pl: { xs: 1, sm: 3 }, pr: 1 }}>
        <FormControl fullWidth variant="outlined">
          <TextField
            value={individual?.lastName}
            onChange={(e) => handleIndividualChange(e.target.value, 'lastName', item.isMandatory)}
            label={item.display}
            name="lastName"
            size="small"
            required={item.isMandatory}
            inputProps={{
              readOnly: isClientSummary,
            }}
            error={validationData?.individuals ? !!validationData?.individuals?.[index]?.lastName : false}
            helperText={validationData?.individuals ? validationData?.individuals?.[index]?.lastName : ''}
            autoComplete="off"
          />
        </FormControl>
      </Grid>
    );
  };

  const getMiddleName = (item) => {
    return (
      <Grid item xs={12} sm={10} md={6} lg={5} sx={{ mt: 2, pl: { xs: 1, sm: 3 }, pr: 1 }}>
        <FormControl fullWidth variant="outlined">
          <TextField
            value={individual?.middleName}
            onChange={(e) => handleIndividualChange(e.target.value, 'middleName', item.isMandatory)}
            label={item.display}
            name="middleName"
            size="small"
            required={item.isMandatory}
            inputProps={{
              readOnly: isClientSummary,
            }}
            error={validationData?.individuals ? !!validationData?.individuals?.[index]?.middleName : false}
            helperText={validationData?.individuals ? validationData?.individuals?.[index]?.middleName : ''}
            autoComplete="off"
          />
        </FormControl>
      </Grid>
    );
  };

  const getGender = (item) => {
    return (
      <Grid item xs={12} sm={10} md={6} lg={5} sx={{ mt: 2, pl: { xs: 1, sm: 3 }, pr: 1 }}>
        <FormControl fullWidth variant="outlined">
          <InputLabel
            size="small"
            error={validationData?.individuals ? !!validationData?.individuals?.[index]?.gender : false}
            required={item.isMandatory}
          >
            {item.display}
          </InputLabel>
          <Select
            value={gender}
            label={item.display}
            name="gender"
            size="small"
            onChange={(e) => handleIndividualChange(e.target.value, 'gender', item.isMandatory)}
            inputProps={{ readOnly: isClientSummary }}
            error={validationData?.individuals ? !!validationData?.individuals?.[index]?.gender : false}
            required={item.isMandatory}
          >
            {!item.isMandatory && gender && (
              <MenuItem value="">
                <em>{t('none')}</em>
              </MenuItem>
            )}
            {genderOptions.map((gender, index) => (
              <MenuItem key={index} value={gender.value}>
                {gender.displayName}
              </MenuItem>
            ))}
          </Select>
          <FormHelperText error>
            {validationData?.individuals ? validationData?.individuals?.[index]?.gender : ''}
          </FormHelperText>
        </FormControl>
      </Grid>
    );
  };

  const getDateOfBirth = (item) => {
    return (
      <Grid item xs={12} sm={10} md={6} lg={5} sx={{ mt: 2, pl: { xs: 1, sm: 3 }, pr: 1 }}>
        <FormControl fullWidth variant="outlined">
          <CambianDatePicker
            label={item.display}
            required={item.isMandatory}
            name="dateOfBirth"
            size="small"
            value={date === null ? date : parseISO(date)}
            isReadOnly={isClientSummary}
            format="yyyy-MM-dd"
            inputFormat="yyyy-MM-dd"
            mask="____-__-__"
            disableFuture={true}
            fullWidth={true}
            onChange={(newValue) =>
              handleIndividualChange(convertInDateFormat(newValue), 'dateOfBirth', item.isMandatory)
            }
            convertDateFormat={convertInDateFormat}
            error={validationData?.individuals && !!validationData?.individuals?.[index]?.dateOfBirth}
            requiredFieldText={t('requiredField')}
            invalidFieldText={t('dateValid')}
          />
          <FormHelperText error>
            {validationData?.individuals ? validationData?.individuals?.[index]?.dateOfBirth : ''}
          </FormHelperText>
        </FormControl>
      </Grid>
    );
  };

  const getEmail = (item) => {
    if (!item.allowMultiple) {
      return (
        <Grid item xs={12} sm={10} md={6} lg={5} sx={{ mt: 1, pl: { xs: 1, sm: 3 }, pr: 1 }}>
          <ContactField
            contact={emails}
            index={0}
            type="email"
            label={item.display}
            required={item.isMandatory}
            value={demographicFields?.contact?.emailAddresses[primaryEmailIndex]?.emailAddress || ''}
            onChange={({ value: updatedEmail, validationError, validationHelperText }) => {
              setShowValidationErrors(true);
              const updatedEmailObj = {
                ...emails[primaryEmailIndex],
                emailAddress: updatedEmail,
                primary: true,
              };
              const updatedEmails = [updatedEmailObj];
              setEmails(updatedEmails);
              demographicFields.contact.emailAddresses = updatedEmails;
              handleContactChange(updatedEmails, 'emailAddresses', item.isMandatory, [
                { error: validationError, helperText: validationHelperText },
              ]);
            }}
            isReadOnly={isClientSummary}
            error={!!validationData?.contact?.emailAddresses?.[primaryEmailIndex]}
            helperText={validationData?.contact?.emailAddresses?.[primaryEmailIndex]}
            isPrimary={true}
            isVerified={primaryEmailVerified && true}
            preferredContactMethod={demographicFields?.preferredContactMethod}
            otpVerificationEnabled={otpVerificationEnabled}
            handleVerificationClick={handleVerificationClick}
            handleVerify={handleVerify}
            sendOtpError={sendOtpError}
            isSendingOtp={isSendingOtp}
            isVerifying={isVerifyingOtp}
            texts={contactFieldTexts}
            CambianTooltip={CambianTooltip}
            Loader={Loader}
          />
        </Grid>
      );
    }

    return (
      <Grid item xs={12} sm={10} md={6} lg={5} sx={{ mt: 1, pl: { xs: 1, sm: 3 }, pr: 1 }}>
        {emails.map((email, emailIndex) => (
          <ContactField
            key={`email-${emailIndex}`}
            contact={emails}
            index={emailIndex}
            type="email"
            label={item.display}
            allowMultiple={item.allowMultiple}
            required={item.isMandatory}
            value={email.emailAddress}
            onChange={({ value: updatedEmail, validationError, validationHelperText }) => {
              setShowValidationErrors(true);
              setEmails((prevEmails) => {
                const updatedEmails = [...prevEmails];
                updatedEmails[emailIndex].emailAddress = updatedEmail;
                demographicFields.contact.emailAddresses = updatedEmails;
                const validationMeta = updatedEmails.map((email) => ({
                  error: email.emailAddress === updatedEmail ? validationError : false,
                  helperText: email.emailAddress === updatedEmail ? validationHelperText : '',
                }));
                handleContactChange(updatedEmails, 'emailAddresses', item.isMandatory, validationMeta);
                return updatedEmails;
              });
            }}
            onSetPrimary={() => {
              const updatedEmails = emails.map((email, i) => ({
                ...email,
                primary: i === emailIndex,
              }));
              setEmails(updatedEmails);
              demographicFields.contact.emailAddresses = updatedEmails;
              handleContactChange(updatedEmails, 'emailAddresses', item.isMandatory);

              const isThisEmailVerified = verifiedValues.has(emails[emailIndex].emailAddress);
              onVerificationSuccess('email', isThisEmailVerified, emails[emailIndex].emailAddress);
            }}
            onDelete={() => {
              const updatedEmails = emails.filter((_, i) => i !== emailIndex);
              if (!updatedEmails.some((email) => email.primary) && updatedEmails.length > 0) {
                updatedEmails[0].primary = true;
              }
              setEmails(updatedEmails);
              demographicFields.contact.emailAddresses = updatedEmails;
              handleContactChange(updatedEmails, 'emailAddresses', item.isMandatory);
            }}
            isReadOnly={isClientSummary}
            error={!!validationData?.contact?.emailAddresses?.[emailIndex]}
            helperText={validationData?.contact?.emailAddresses?.[emailIndex]}
            isPrimary={email.primary}
            isVerified={email.primary ? primaryEmailVerified : verifiedValues.has(email.emailAddress)}
            otpVerificationEnabled={otpVerificationEnabled}
            preferredContactMethod={demographicFields?.preferredContactMethod}
            handleVerificationClick={handleVerificationClick}
            handleVerify={handleVerify}
            sendOtpError={sendOtpError}
            isSendingOtp={isSendingOtp}
            isVerifying={isVerifyingOtp}
            texts={contactFieldTexts}
            CambianTooltip={CambianTooltip}
            Loader={Loader}
          />
        ))}
        <Button
          variant="text"
          startIcon={<Add />}
          onClick={() => {
            const newEmail = { emailAddress: '', primary: emails.length === 0 };
            setEmails((prevEmails) => [...prevEmails, newEmail]);
            demographicFields.contact.emailAddresses.push(newEmail);
          }}
          sx={{
            p: '15px 1px',
            cursor: 'pointer',
            fontSize: '15px',
            '&:hover': { backgroundColor: 'transparent', color: '#23527c' },
          }}
        >
          {t('addEmail')}
        </Button>
      </Grid>
    );
  };

  const getPhoneNumber = (item) => {
    if (!item.allowMultiple) {
      return (
        <Grid item xs={12} sm={10} md={6} lg={5} sx={{ mt: 1, pl: { xs: 1, sm: 3 }, pr: 1 }}>
          <ContactField
            contact={phones}
            index={0}
            type="phone"
            label={item.display}
            required={item.isMandatory}
            value={phones[primaryPhoneIndex]?.phoneNumber || ''}
            onChange={({ value: updatedPhone, validationError, validationHelperText }) => {
              setShowValidationErrors(true);
              const updatedPhoneObj = {
                ...phones[primaryPhoneIndex],
                phoneNumber: updatedPhone,
                primary: true,
              };
              const updatedPhones = [updatedPhoneObj];
              setPhones(updatedPhones);
              demographicFields.contact.phoneNumbers = updatedPhones;
              handleContactChange(updatedPhones, 'phoneNumbers', item.isMandatory, [
                { error: validationError, helperText: validationHelperText },
              ]);
            }}
            isReadOnly={isClientSummary}
            error={!!validationData?.contact?.phoneNumbers?.[primaryPhoneIndex]}
            helperText={validationData?.contact?.phoneNumbers?.[primaryPhoneIndex]}
            isPrimary={true}
            isVerified={primaryPhoneVerified && true}
            otpVerificationEnabled={otpVerificationEnabled}
            preferredContactMethod={demographicFields?.preferredContactMethod}
            handleVerificationClick={handleVerificationClick}
            handleVerify={handleVerify}
            sendOtpError={sendOtpError}
            isSendingOtp={isSendingOtp}
            isVerifying={isVerifyingOtp}
            texts={contactFieldTexts}
            CambianTooltip={CambianTooltip}
            Loader={Loader}
            InputProps={{
              inputComponent: MaskTextField,
              inputProps: {
                mask: '(###) ###-####',
                definitions: {
                  '#': /[0-9]/,
                },
              },
            }}
          />
        </Grid>
      );
    }

    return (
      <Grid item xs={12} sm={10} md={6} lg={5} sx={{ mt: 1, pl: { xs: 1, sm: 3 }, pr: 1 }}>
        {phones.map((phone, phoneIndex) => (
          <ContactField
            key={`phone-${phoneIndex}`}
            contact={phones}
            index={phoneIndex}
            type="phone"
            label={item.display}
            required={item.isMandatory}
            allowMultiple={item.allowMultiple}
            value={phone.phoneNumber}
            onChange={({ value: updatedPhone, validationError, validationHelperText }) => {
              setShowValidationErrors(true);
              setPhones((prevPhones) => {
                const updatedPhones = [...prevPhones];
                updatedPhones[phoneIndex] = {
                  ...updatedPhones[phoneIndex],
                  phoneNumber: updatedPhone,
                };
                demographicFields.contact.phoneNumbers = updatedPhones;
                const validationMeta = updatedPhones.map((phone) => ({
                  error: phone.phoneNumber === updatedPhone ? validationError : false,
                  helperText: phone.phoneNumber === updatedPhone ? validationHelperText : '',
                }));
                handleContactChange(updatedPhones, 'phoneNumbers', item.isMandatory, validationMeta);
                return updatedPhones;
              });
            }}
            onSetPrimary={() => {
              const updatedPhones = phones.map((phone, i) => ({
                ...phone,
                primary: i === phoneIndex,
              }));
              setPhones(updatedPhones);
              demographicFields.contact.phoneNumbers = updatedPhones;
              handleContactChange(updatedPhones, 'phoneNumbers', item.isMandatory);
              const isThisPhoneVerified = verifiedValues.has(phones[phoneIndex].phoneNumber);
              onVerificationSuccess('phone', isThisPhoneVerified, phones[phoneIndex].phoneNumber);
            }}
            onDelete={() => {
              const updatedPhones = phones.filter((_, i) => i !== phoneIndex);
              if (!updatedPhones.some((phone) => phone.primary) && updatedPhones.length > 0) {
                updatedPhones[0].primary = true;
              }
              setPhones(updatedPhones);
              demographicFields.contact.phoneNumbers = updatedPhones;
              handleContactChange(updatedPhones, 'phoneNumbers', item.isMandatory);
            }}
            isReadOnly={isClientSummary}
            error={!!validationData?.contact?.phoneNumbers?.[phoneIndex]}
            helperText={validationData?.contact?.phoneNumbers?.[phoneIndex]}
            isPrimary={phone.primary}
            isVerified={phone.primary ? primaryPhoneVerified : verifiedValues.has(phone.phoneNumber)}
            otpVerificationEnabled={otpVerificationEnabled}
            preferredContactMethod={demographicFields?.preferredContactMethod}
            handleVerificationClick={handleVerificationClick}
            handleVerify={handleVerify}
            sendOtpError={sendOtpError}
            isSendingOtp={isSendingOtp}
            isVerifying={isVerifyingOtp}
            texts={contactFieldTexts}
            CambianTooltip={CambianTooltip}
            Loader={Loader}
            InputProps={{
              inputComponent: MaskTextField,
              inputProps: {
                mask: '(###) ###-####',
                definitions: {
                  '#': /[0-9]/,
                },
              },
            }}
          />
        ))}
        <Button
          variant="text"
          startIcon={<Add />}
          onClick={() => {
            const newPhone = { phoneNumber: '', primary: phones.length === 0 };
            setPhones((prevPhones) => [...prevPhones, newPhone]);
            demographicFields.contact.phoneNumbers.push(newPhone);
          }}
          sx={{
            p: '15px 1px',
            cursor: 'pointer',
            fontSize: '15px',
            '&:hover': { backgroundColor: 'transparent', color: '#23527c' },
          }}
        >
          {t('addPhone')}
        </Button>
      </Grid>
    );
  };
  const findPreferredContactMethodValue = () => {
    if (!preferredContactMethod) {
      return '';
    }
    if (preferredContactMethod === 'Email') {
      return t('email');
    } else if (preferredContactMethod === 'Phone') {
      return t('phone');
    }
    const preferredMethod = preferredContactMethod.toLowerCase();
    for (const option of contactMethodOptions) {
      if (option.value.toLowerCase() === preferredMethod) {
        return option.value;
      }
    }
    if (preferredMethod.includes('email')) {
      const emailOption = contactMethodOptions.find((option) => option.id === 'EMAIL');
      if (emailOption) {
        return emailOption.value;
      }
    }
    if (preferredMethod.includes('phone')) {
      const phoneOption = contactMethodOptions.find((option) => option.id === 'PHONE');
      if (phoneOption) {
        return phoneOption.value;
      }
    }
    return preferredContactMethod;
  };
  const getPreferredContactMethod = (item) => {
    return (
      <Grid container>
        <Grid item xs={12} sm={10} md={6} lg={5} sx={{ mt: 2, pl: { xs: 1, sm: 3 }, pr: 1 }}>
          <FormControl fullWidth variant="outlined">
            <InputLabel size="small" error={!!validationData?.preferredContactMethod} required={item.isMandatory}>
              {item.display}
            </InputLabel>
            <Select
              label={t('preferredContactMethod')}
              value={findPreferredContactMethodValue()}
              onChange={(e) => {
                let internalValue = e.target.value;
                if (internalValue.toLowerCase() === t('email').toLowerCase()) {
                  internalValue = 'Email';
                } else if (internalValue.toLowerCase() === t('phone').toLowerCase()) {
                  internalValue = 'Phone';
                }
                handlePreferredContactMethodCallback(internalValue);
              }}
              name="preferredContactMethod"
              size="small"
              required={item.isMandatory}
              error={!!validationData?.preferredContactMethod}
            >
              {!item.isMandatory && preferredContactMethod && (
                <MenuItem value="">
                  <em>{t('none')}</em>
                </MenuItem>
              )}
              {contactMethodOptions.map(
                (option) =>
                  fields.find((field) => field.code === option.id) && (
                    <MenuItem key={option.id} value={option.value} disabled={option.disabled}>
                      {option.value}
                    </MenuItem>
                  ),
              )}
            </Select>
            <FormHelperText error>
              {validationData?.individuals ? validationData?.preferredContactMethod : ''}
            </FormHelperText>{' '}
          </FormControl>
        </Grid>
      </Grid>
    );
  };

  const getNotification = (item) => {
    return (
      <Grid container>
        <Grid item xs={12} sm={10} md={6} lg={5} sx={{ mt: 2, pl: { xs: 1, sm: 3 }, pr: 1 }}>
          <FormControl fullWidth variant="outlined">
            <InputLabel size="small" required={item.isMandatory} error={!!validationData?.subscribeToNotifications}>
              {item.display}
            </InputLabel>
            <Select
              value={demographicFields.subscribeToNotifications ?? true}
              defaultValue={true}
              label={item.display}
              name="subscribeToNotifications"
              size="small"
              onChange={(e) => handleIndividualChange(e.target.value, 'subscribeToNotifications', item.isMandatory)}
              inputProps={{ readOnly: isClientSummary }}
              required={item.isMandatory}
              error={!!validationData?.subscribeToNotifications}
            >
              {allowNotificationsOptions.map((allowNotificationsOption, index) => (
                <MenuItem
                  key={index}
                  value={allowNotificationsOption.value}
                  disabled={allowNotificationsOption.disabled}
                >
                  {allowNotificationsOption.displayName}
                </MenuItem>
              ))}
            </Select>
            <FormHelperText error>{validationData?.subscribeToNotifications || ''}</FormHelperText>{' '}
          </FormControl>
        </Grid>
      </Grid>
    );
  };

  const getAddress = (item) => {
    return (
      <AddressFields
        demographicFields={demographicFields}
        item={item}
        addresses={addresses}
        setAddresses={setAddresses}
        validationData={validationData}
        isClientSummary={isClientSummary}
        handleContactChange={handleContactChange}
        setShowValidationErrors={setShowValidationErrors}
        t={t}
      />
    );
  };
  const getHealthcareId = (item) => {
    return (
      <HealthcareIdFields
        demographicFields={demographicFields}
        item={item}
        handleIndividualChange={handleIndividualChange}
        setShowValidationErrors={setShowValidationErrors}
        healthcareIds={healthcareIds}
        setHealthcareIds={setHealthcareIds}
        validationData={validationData}
        isClientSummary={isClientSummary}
        index={index}
        t={t}
      />
    );
  };

  return (
    <Stack direction="column">
      <Box>
        {!dataValidation.isDataEmpty(fields) &&
          fields.sort(sortFields).map((item, index) => {
            return (
              <Grid container key={index}>
                {item.code === 'PREFERRED_CONTACT_METHOD' && getPreferredContactMethod(item)}
                {item.code === 'PHONE' && getPhoneNumber(item)}
                {item.code === 'EMAIL' && getEmail(item)}
                {item.code === 'ADDRESS' && getAddress(item)}
                {item.code === 'FIRST_NAME' && getFirstName(item)}
                {item.code === 'LAST_NAME' && getLastName(item)}
                {item.code === 'MIDDLE_NAME' && getMiddleName(item)}
                {item.code === 'DATE_OF_BIRTH' && getDateOfBirth(item)}
                {item.code === 'GENDER' && getGender(item)}
                {item.code === 'IDENTIFICATION' && getHealthcareId(item)}
                {item.code === 'NOTIFICATIONS' && getNotification(item)}
              </Grid>
            );
          })}
      </Box>
    </Stack>
  );
};
