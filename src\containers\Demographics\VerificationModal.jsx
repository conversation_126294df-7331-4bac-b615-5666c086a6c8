import React, { useState, useEffect } from 'react';
import {
  FormControl,
  TextField,
  Grid,
  Stack,
  <PERSON><PERSON><PERSON>,
  <PERSON>ton,
  Dialog,
  DialogContent,
  FormHelperText,
  Link,
  Box,
  IconButton,
  useMediaQuery,
  InputLabel,
  CircularProgress,
} from '@mui/material';
import {
  Star,
  StarOutline,
  Verified as VerifiedIcon,
  VerifiedOutlined as Unverified,
  Close,
} from '@mui/icons-material';
import PhoneInput from 'react-phone-input-2';
import 'react-phone-input-2/lib/style.css';
import { isValidPhoneNumber } from 'libphonenumber-js';

const defaultTexts = {
  verifyEmail: 'Enter Verification Code',
  verifyPhone: 'Enter Verification Code',
  verificationSubtitle: 'A verification code has been sent to ',
  verificationSubtitleEmail: '. Please enter it to verify your email address.',
  verificationSubtitlePhone: '. Please enter it to verify your phone number.',
  verificationCode: 'Verification code',
  verificationError: 'We are having trouble verifying that code. Please try again.',
  verify: 'Verify',
  clickHere: 'Click here',
  clickToVerify: 'Click to verify',
  somethingWentWrong: 'Something went wrong. Please try again.',
  verified: 'Verified',
  primary: 'Primary',
  setAsPrimary: 'Set as Primary',
  deleteEmail: 'Delete email',
  deletePhone: 'Delete phone',
  resendCode: 'Resend Verification Code',
  toVerifyEmail: 'You must verify your email to continue. ',
  toVerifyPhone: 'You must verify your phone to continue. ',
};

const VerificationModal = ({
  open,
  onClose,
  contactValue,
  contactType,
  onVerify,
  isVerifying,
  isSendingOtp,
  texts = defaultTexts,
  Loader,
  handleVerificationClick,
}) => {
  const [verificationCode, setVerificationCode] = useState('');
  const [error, setError] = useState('');
  const isXs = useMediaQuery((theme) => theme.breakpoints.down('sm'));

  const handleVerify = () => {
    setError('');
    onVerify(verificationCode, {
      onFailed: () => setError(texts.verificationError),
    });
  };

  const handleResend = () => {
    handleVerificationClick({
      type: contactType === 'email' ? 'Email' : 'Phone',
      value: contactValue,
      onSuccess: () => {},
    });
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      sx={{
        '& .MuiDialog-paper': {
          maxWidth: '100%',
          width: { xs: '100%', sm: '50ch' },
          margin: { xs: 0, sm: 2 },
          position: 'absolute',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
        },
      }}
    >
      <IconButton
        onClick={onClose}
        size="medium"
        sx={{ justifyContent: 'flex-end', position: 'absolute', top: 1, right: 1 }}
      >
        <Close sx={{ cursor: 'pointer', fontSize: '20px' }} />
      </IconButton>

      <DialogContent>
        <Box position="relative" minHeight={200}>
          <Stack direction="column" spacing={1.5}>
            <Typography sx={{ fontSize: { xs: 24, sm: 30 }, fontWeight: 500 }}>
              {contactType === 'email' ? texts.verifyEmail : texts.verifyPhone}
            </Typography>
            <Typography sx={{ fontSize: { xs: 14, sm: 15 } }}>
              {texts.verificationSubtitle}
              <Typography component="span" variant="inherit" sx={{ fontWeight: 'bold' }}>
                {contactValue}
              </Typography>
              <Typography component="span" sx={{ fontSize: { xs: 14, sm: 15 } }}>
                {contactType === 'email' ? texts.verificationSubtitleEmail : texts.verificationSubtitlePhone}
              </Typography>
            </Typography>

            <TextField
              autoFocus
              size="small"
              disabled={isVerifying || isSendingOtp}
              label={texts.verificationCode}
              value={verificationCode}
              onChange={(e) => setVerificationCode(e.target.value?.trim())}
              inputProps={{ style: { fontSize: isXs ? 14 : 17 } }}
              InputLabelProps={{
                style: {
                  fontSize: isXs ? 13 : 16,
                  marginTop: isXs ? 2 : undefined,
                },
              }}
            />

            {error && (
              <Typography sx={{ fontSize: { xs: 11, sm: 12 } }} color="error">
                {error}
              </Typography>
            )}

            <Box sx={{ display: 'flex', justifyContent: 'center', width: '100%' }}>
              <Button
                variant="contained"
                onClick={handleVerify}
                disabled={!verificationCode || isVerifying || isSendingOtp}
                sx={{
                  fontSize: { xs: 15, sm: 16 },
                  fontWeight: 500,
                  padding: '8px 16px',
                  lineHeight: 1,
                }}
              >
                {isVerifying ? <CircularProgress size={isXs ? 15 : 19} /> : texts.verify}
              </Button>
            </Box>

            <Typography
              sx={{
                fontSize: { xs: 13, sm: 14 },
                mt: 1,
                display: 'flex',
                alignItems: 'center',
              }}
            >
              <Link
                component="button"
                variant="body2"
                onClick={handleResend}
                sx={{
                  color: 'primary.main',
                  textDecoration: 'underline',
                  fontSize: 'inherit',
                  ml: 0.5,
                  mr: 1,
                  cursor: 'pointer',
                }}
              >
                {texts.resendCode}
              </Link>{' '}
              {isSendingOtp && <CircularProgress size={isXs ? 15 : 19} />}
            </Typography>
          </Stack>
        </Box>
      </DialogContent>
    </Dialog>
  );
};
const ContactField = ({
  contact,
  index,
  type,
  label,
  required,
  value,
  onChange,
  onSetPrimary,
  onDelete,
  isReadOnly,
  error,
  helperText,
  isPrimary,
  isVerified,
  otpVerificationEnabled,
  preferredContactMethod,
  InputProps,
  placeholder,
  allowMultiple,
  handleVerificationClick,
  handleVerify,
  isSendingOtp,
  isVerifying,
  sendOtpError,
  Loader,
  CambianTooltip,
  texts = defaultTexts,
  showStarIcon = true,
}) => {
  const [verificationModalOpen, setVerificationModalOpen] = useState(false);
  const isCurrentValueVerified = isVerified;
  const EMAIL_REGEX_PATTERN = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,6}$/;
  const [validationError, setValidationError] = useState(false);
  const [validationHelperText, setValidationHelperText] = useState('');

  useEffect(() => {
    if (type === 'phone' || type === 'fax') {
      const { error, helperText } = type === 'phone' ? validatePhone(value) : validateFax(value);
      setValidationError(error);
      setValidationHelperText(helperText);
    } else if (type === 'email') {
      const { error, helperText } = validateEmail(value);
      setValidationError(error);
      setValidationHelperText(helperText);
    }
  }, [value, type]);

  const validatePhone = (input) => {
    if (!input || input === '+') {
      return { error: false, helperText: '' };
    }
    const isValid = isValidPhoneNumber(input);
    return {
      error: !isValid,
      helperText: !isValid ? 'Invalid phone number' : '',
    };
  };

  const validateFax = (input) => {
    if (!input || input === '+') {
      return { error: false, helperText: '' };
    }
    const isValid = isValidPhoneNumber(input);
    return {
      error: !isValid,
      helperText: !isValid ? 'Invalid fax number' : '',
    };
  };

  const validateEmail = (input) => {
    if (!input) {
      return { error: false, helperText: '' };
    }
    const domainParts = input.split('@')[1]?.split('.') || [];
    const tld = domainParts[domainParts.length - 1] || '';
    const error = !EMAIL_REGEX_PATTERN.test(input) || domainParts.length > 3 || tld.length < 2 || tld.length > 6;
    return {
      error,
      helperText: error ? 'Invalid email' : '',
    };
  };

  const handleInputChange = (e) => {
    const newValue = e.target.value === '' ? null : e.target.value;
    const { error, helperText } = validateEmail(newValue);
    setValidationError(error);
    setValidationHelperText(helperText);
    onChange({
      value: newValue,
      validationError: error,
      validationHelperText: helperText,
    });
  };

  const handlePhoneChange = (phoneNumber) => {
    if (!phoneNumber || phoneNumber === '+') {
      onChange({
        value: null,
        validationError: false,
        validationHelperText: '',
      });
      return;
    }
    const fullNumber = '+' + phoneNumber;
    const { error, helperText } = type === 'phone' ? validatePhone(fullNumber) : validateFax(fullNumber);
    setValidationError(error);
    setValidationHelperText(helperText);
    onChange({ value: fullNumber, validationError: error, validationHelperText: helperText });
  };

  const isEmailPreferred = preferredContactMethod === 'Email';
  const isPhonePreferred = preferredContactMethod === 'Phone';
  const isVerificationRequired =
    isPrimary &&
    ((type === 'email' && (required || isEmailPreferred)) || (type === 'phone' && (required || isPhonePreferred)));

  const openVerificationModal = () => {
    handleVerificationClick({
      type: type === 'email' ? 'Email' : 'Phone',
      value: value,
      onSuccess: () => setVerificationModalOpen(true),
    });
  };

  const handleModalVerify = (code, { onFailed }) => {
    handleVerify({
      type: type === 'email' ? 'Email' : 'Phone',
      value: value,
      otp: code,
      onSuccess: (response) => {
        if (response?.result === 'VERIFIED') {
          setVerificationModalOpen(false);
        } else if (response?.result === 'FAILED') {
          onFailed();
        }
      },
    });
  };

  return (
    <Grid container alignItems="flex-start" spacing={1} sx={{ mt: 1, maxWidth: '500px' }}>
      <Grid item xs={12} sx={{ display: 'flex', maxWidth: '500px' }}>
        <Box sx={{ flexGrow: 1, minWidth: 0 }}>
          <FormControl fullWidth>
            {type === 'phone' || type === 'fax' ? (
              isReadOnly ? (
                <TextField value={value || ''} InputProps={{ readOnly: true }} disabled fullWidth />
              ) : (
                <FormControl fullWidth error={error || validationError} variant="outlined">
                  <InputLabel
                    shrink
                    htmlFor="phone-input"
                    error={error || validationError}
                    required={required}
                    sx={{
                      backgroundColor: 'white',
                      padding: '0 5px',
                      transform: 'translate(14px, -9px) scale(0.75)',
                    }}
                  >
                    {label}
                  </InputLabel>

                  <Box
                    sx={{
                      '& .react-tel-input': {
                        '& .form-control': {
                          width: '100%',
                          height: '40px',
                          borderRadius: '4px',
                          fontSize: '16px',
                          border: error || validationError ? '1px solid #d32f2f' : '1px solid rgba(0, 0, 0, 0.23)',
                          '&:hover, &:focus': {
                            border: error || validationError ? '1px solid #d32f2f' : '1px solid rgba(0, 0, 0, 0.87)',
                          },
                        },
                        '& .flag-dropdown': {
                          border: 'none',
                          backgroundColor: 'transparent',
                          '&:hover, &:focus, &.open': {
                            backgroundColor: 'transparent',
                            border: 'none',
                          },
                          '& .selected-flag': {
                            backgroundColor: 'transparent !important',
                            borderRight: 'none !important',
                            '&:hover, &:focus': {
                              backgroundColor: 'transparent !important',
                            },
                          },
                        },
                      },
                    }}
                  >
                    <PhoneInput
                      country={'ca'}
                      value={value === null ? '' : value ? value.replace('+', '') : ''}
                      onChange={handlePhoneChange}
                      disabled={isReadOnly}
                      inputProps={{
                        id: 'phone-input',
                        required: required,
                        disabled: isReadOnly,
                        placeholder: placeholder || '',
                      }}
                      containerStyle={{ width: '100%' }}
                      inputStyle={{ width: '100%' }}
                      buttonStyle={{
                        border: 'none',
                        borderRight: 'none',
                        background: 'transparent',
                      }}
                      dropdownStyle={{
                        width: '300px',
                      }}
                      preferredCountries={['ca']}
                      enableSearch={!isReadOnly}
                      disableDropdown={isReadOnly}
                      disableCountryCode={isReadOnly}
                      countryCodeEditable={!isReadOnly}
                    />
                  </Box>

                  {(helperText || validationHelperText) && (
                    <FormHelperText error={error || validationError}>
                      {helperText || validationHelperText}
                    </FormHelperText>
                  )}
                </FormControl>
              )
            ) : (
              <TextField
                value={value}
                onChange={handleInputChange}
                type={type}
                label={label}
                size="small"
                fullWidth
                disabled={isReadOnly}
                required={required}
                error={error || validationError}
                helperText={helperText || validationHelperText}
                InputProps={InputProps}
                placeholder={placeholder}
                InputLabelProps={{
                  shrink: !!value,
                }}
              />
            )}
            {sendOtpError && sendOtpError[value] && value && !error && (
              <FormHelperText error sx={{ padding: 0, marginTop: 0 }}>
                {texts.somethingWentWrong}{' '}
                <Link
                  sx={{ '&:hover': { cursor: 'pointer' }, color: 'inherit', textDecorationColor: 'inherit' }}
                  onClick={openVerificationModal}
                >
                  {texts.clickHere}
                </Link>
              </FormHelperText>
            )}
            {otpVerificationEnabled &&
              isPrimary &&
              !isCurrentValueVerified &&
              value &&
              !error &&
              !validationError &&
              isVerificationRequired &&
              !sendOtpError[value] && (
                <FormHelperText sx={{ padding: 0, marginTop: 0, color: '#d32f2f' }}>
                  {type === 'email' ? texts.toVerifyEmail : texts.toVerifyPhone}
                  <Link
                    sx={{
                      '&:hover': { cursor: 'pointer' },
                      textDecoration: 'underline',
                      cursor: 'pointer',
                      color: '#d32f2f',
                    }}
                    onClick={openVerificationModal}
                  >
                    {texts.clickHere}
                  </Link>
                </FormHelperText>
              )}
          </FormControl>
        </Box>
        <Box sx={{ display: 'flex', alignItems: 'flex-start', pl: 1, pt: 1, gap: 0.5 }}>
          {!error && value && !validationError && (
            <Box mr={0.5}>
              {isCurrentValueVerified ? (
                <CambianTooltip title={texts.verified}>
                  <VerifiedIcon color="primary" sx={{ fontSize: '20px', ml: 1 }} />
                </CambianTooltip>
              ) : (
                <Box sx={{ position: 'relative' }}>
                  {isSendingOtp && value && (
                    <Box sx={{ position: 'absolute', top: '-5px', left: '-30px' }}>
                      <Loader active={true} />
                    </Box>
                  )}
                  <CambianTooltip title={texts.clickToVerify}>
                    <Unverified
                      onClick={openVerificationModal}
                      disabled={isSendingOtp}
                      color="primary"
                      sx={{ fontSize: '20px', cursor: 'pointer', ml: 1 }}
                    />
                  </CambianTooltip>
                </Box>
              )}
            </Box>
          )}
          {showStarIcon && (
            <>
              {isPrimary ? (
                <CambianTooltip title={texts.primary}>
                  <Star color="primary" sx={{ fontSize: '20px', ml: 1 }} />
                </CambianTooltip>
              ) : (
                <CambianTooltip title={texts.setAsPrimary}>
                  <StarOutline
                    color="primary"
                    sx={{ cursor: 'pointer', fontSize: '20px', ml: 1 }}
                    onClick={onSetPrimary}
                  />
                </CambianTooltip>
              )}
            </>
          )}
          {allowMultiple && contact.length > 1 && (
            <CambianTooltip title={type === 'email' ? texts.deleteEmail : texts.deletePhone}>
              <Close color="action" sx={{ cursor: 'pointer', ml: 1, fontSize: '20px' }} onClick={onDelete} />
            </CambianTooltip>
          )}
        </Box>
      </Grid>
      <VerificationModal
        open={verificationModalOpen}
        onClose={() => setVerificationModalOpen(false)}
        contactValue={value}
        contactType={type}
        onVerify={handleModalVerify}
        handleVerificationClick={handleVerificationClick}
        isSendingOtp={isSendingOtp}
        isVerifying={isVerifying}
        Loader={Loader}
        texts={texts}
      />
    </Grid>
  );
};

export { ContactField };
