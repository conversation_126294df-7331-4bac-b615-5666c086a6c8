import moment from 'moment';
import { TIME_SLOT_MINUTES, TIME_FORMAT_LONG } from '../../constants/index';
/**
 * format date in dd/mm/yyyy
 * @param date
 */
const formatDateDDMMYYY = (date) => {
  const _date = date.getDate() < 10 ? '0' + date.getDate() : date.getDate();
  const _month = date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1;

  let formattedDate = date.getFullYear() + '-' + _month + '-' + _date;
  return formattedDate;
};

const formatDateName = (dateParameter) => {
  let date = new Date(dateParameter);
  const _date = date.getDate() < 10 ? date.getDate() : date.getDate();

  let formattedDate =
    weeks[date.getDay()] + ', ' + fullMonths[date.getMonth()] + ' ' + _date + ',  ' + date.getFullYear();
  return formattedDate;
};

const formatDate = (dateParameter) => {
  /**
   * Date format January 01, 1970
   */
  let formattedDate = moment(dateParameter).format('MMMM DD, YYYY');
  return formattedDate;
};

/**
 * format time in 0:00 AM/PM
 * We are getting 2020-12-03T09:00:00.000+00:00 format and extracting time
 * (excluding timezone) and converting it into 12hr format using moment library
 * @param date
 */
const convertTimeIntoTwelveHourFormat = (dateTimeWithTimeZoneFormat, formate) => {
  let date = moment.parseZone(dateTimeWithTimeZoneFormat).format(formate);
  return date;
};

const convertTimeIntoTwelveHourFormatIgnoreZone = (dateTimeWithTimeZoneFormat, formate) => {
  //let temp = moment(dateTimeWithTimeZoneFormat).utcOffset(dateTimeWithTimeZoneFormat).format('MMMM Do YYYY, h:mm:ss a');
  return moment(dateTimeWithTimeZoneFormat).utcOffset(dateTimeWithTimeZoneFormat).format(formate);
};

const convertInDateFormat = (dateParameter) => {
  /**
   * Date format 02/04/2021
   */
  let formattedDate = moment(dateParameter).format('YYYY-MM-DD');
  return formattedDate;
};
const convertInDateTimeFormat = (dateParameter) => {
  /**
   * Date format 02/04/2021
   */
  let formattedDate = moment(dateParameter).format('YYYY-MM-DD HH:mm:ss');
  return formattedDate;
};

const convertLongDateTimeFormat = (dateParameter) => {
  /**
   * Date format 02/04/2021
   */
  let formattedDate = moment.parseZone(dateParameter).format(TIME_FORMAT_LONG);
  return formattedDate;
};
/**
 * This function add 15 minute to the given time
 * @param {*} scheduleTimeDate
 */
const addTimeInAMPM = (scheduleTimeDate, timeObj = {}) => {
  let start = moment(timeObj.start);
  let end = moment(timeObj.end);
  var diffInMinutes = end.diff(start, 'minutes');
  let time_interval = diffInMinutes ? diffInMinutes : TIME_SLOT_MINUTES;
  var time = moment(scheduleTimeDate, ['hh:mm aa']).add(time_interval, 'minutes').format('hh:mm aa');
  return time;
};
/**
 *
 *
 */
//datenow
const date = new Date();
export const dateNow = date.toISOString();
/**
 *
 *
 */

const weekDays = {
  mon: 'Monday',
  tue: 'Tuesday',
  wed: 'Wednesday',
  thu: 'Thursday',
  fri: 'Friday',
  sat: 'Saturday',
  sun: 'Sunday',
};

const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

const fullMonths = [
  'January',
  'February',
  'March',
  'April',
  'May',
  'June',
  'July',
  'August',
  'September',
  'October',
  'November',
  'December',
];

const weeks = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];

/**
 *
 * @param {*} dateTime
 */
const formatDateMMDDYYYY = (d) => {
  const dateTime = new Date(d);
  const month = dateTime.getMonth();
  const _monthStr = months[month];
  const _date = dateTime.getDate() < 10 ? '0' + dateTime.getDate() : dateTime.getDate();
  const _fullYear = dateTime.getFullYear();
  const strDateTime = _monthStr + ' ' + _date + ', ' + _fullYear;
  return strDateTime;
};

/**
 *
 */
export {
  formatDateDDMMYYY,
  convertTimeIntoTwelveHourFormat,
  convertTimeIntoTwelveHourFormatIgnoreZone,
  formatDateMMDDYYYY,
  fullMonths,
  convertInDateFormat,
  formatDateName,
  addTimeInAMPM,
  weeks,
  weekDays,
  formatDate,
  convertInDateTimeFormat,
  convertLongDateTimeFormat,
};
